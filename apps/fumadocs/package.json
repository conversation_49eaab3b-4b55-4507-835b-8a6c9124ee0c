{"name": "fumadocs", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev --port=4000", "build": "vite build && tsc --noEmit", "start": "node .output/server/index.mjs"}, "dependencies": {"@tanstack/react-router": "^1.131.30", "@tanstack/react-router-devtools": "^1.131.30", "@tanstack/react-start": "^1.131.30", "fumadocs-core": "15.7.11", "fumadocs-mdx": "11.9.0", "fumadocs-ui": "15.7.11", "lucide-static": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "vite": "^7.1.3"}, "devDependencies": {"@tailwindcss/vite": "^4.1.12", "@types/mdx": "^2.0.13", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4"}}