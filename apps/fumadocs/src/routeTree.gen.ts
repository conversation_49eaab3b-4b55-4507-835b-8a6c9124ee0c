/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DocsSplatRouteImport } from './routes/docs/$'
import { ServerRoute as ApiSearchServerRouteImport } from './routes/api/search'

const rootServerRouteImport = createServerRootRoute()

const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DocsSplatRoute = DocsSplatRouteImport.update({
  id: '/docs/$',
  path: '/docs/$',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiSearchServerRoute = ApiSearchServerRouteImport.update({
  id: '/api/search',
  path: '/api/search',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/docs/$': typeof DocsSplatRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/docs/$': typeof DocsSplatRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/docs/$': typeof DocsSplatRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/docs/$'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/docs/$'
  id: '__root__' | '/' | '/docs/$'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DocsSplatRoute: typeof DocsSplatRoute
}
export interface FileServerRoutesByFullPath {
  '/api/search': typeof ApiSearchServerRoute
}
export interface FileServerRoutesByTo {
  '/api/search': typeof ApiSearchServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/search': typeof ApiSearchServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/search'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/search'
  id: '__root__' | '/api/search'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiSearchServerRoute: typeof ApiSearchServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/docs/$': {
      id: '/docs/$'
      path: '/docs/$'
      fullPath: '/docs/$'
      preLoaderRoute: typeof DocsSplatRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/search': {
      id: '/api/search'
      path: '/api/search'
      fullPath: '/api/search'
      preLoaderRoute: typeof ApiSearchServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DocsSplatRoute: DocsSplatRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiSearchServerRoute: ApiSearchServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
