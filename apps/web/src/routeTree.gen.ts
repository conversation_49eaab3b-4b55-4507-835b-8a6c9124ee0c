/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TodosRouteImport } from './routes/todos'
import { Route as LoginRouteImport } from './routes/login'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as AiRouteImport } from './routes/ai'
import { Route as IndexRouteImport } from './routes/index'

const TodosRoute = TodosRouteImport.update({
  id: '/todos',
  path: '/todos',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AiRoute = AiRouteImport.update({
  id: '/ai',
  path: '/ai',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/ai': typeof AiRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/todos': typeof TodosRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/ai': typeof AiRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/todos': typeof TodosRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/ai': typeof AiRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/todos': typeof TodosRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/ai' | '/dashboard' | '/login' | '/todos'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/ai' | '/dashboard' | '/login' | '/todos'
  id: '__root__' | '/' | '/ai' | '/dashboard' | '/login' | '/todos'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AiRoute: typeof AiRoute
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  TodosRoute: typeof TodosRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/todos': {
      id: '/todos'
      path: '/todos'
      fullPath: '/todos'
      preLoaderRoute: typeof TodosRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/ai': {
      id: '/ai'
      path: '/ai'
      fullPath: '/ai'
      preLoaderRoute: typeof AiRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AiRoute: AiRoute,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  TodosRoute: TodosRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
