{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"dotenv": "^17.2.1", "zod": "^4.0.2", "@orpc/server": "^1.8.6", "@orpc/client": "^1.8.6", "@orpc/openapi": "^1.8.6", "@orpc/zod": "^1.8.6", "hono": "^4.8.2", "drizzle-orm": "^0.44.2", "@neondatabase/serverless": "^1.0.1", "ws": "^8.18.3", "ai": "^5.0.39", "@ai-sdk/google": "^2.0.13", "better-auth": "^1.3.9"}, "devDependencies": {"tsdown": "^0.14.1", "typescript": "^5.8.2", "@types/bun": "^1.2.6", "drizzle-kit": "^0.31.2", "@types/ws": "^8.18.1"}}