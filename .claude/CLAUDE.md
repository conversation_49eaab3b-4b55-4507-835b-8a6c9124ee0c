# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a modern TypeScript monorepo using the Better-T-Stack, combining TanStack Start, Hono, ORPC, and Drizzle ORM. The project uses Turborepo for monorepo management and Biome/Ultracite for code quality and linting.

## Architecture

### Monorepo Structure
- **Root**: Contains shared configuration and build orchestration
- **Apps**:
  - `apps/web/` - Frontend React application using TanStack Start
  - `apps/server/` - Backend API using Hono framework
  - `apps/fumadocs/` - Documentation site using Fumadocs

### Key Technology Stack
- **Frontend**: React 19, TanStack Router (for SSR), TanStack Query, Radix UI, Tailwind CSS
- **Backend**: Hono (server framework), ORPC (type-safe APIs), Drizzle ORM
- **Database**: PostgreSQL with Drizzle ORM and migrations
- **Authentication**: Better-Auth
- **Build System**: Turbo (Turborepo) for monorepo orchestration
- **Code Quality**: Biome linter/formatter with Ultracite rules
- **Package Manager**: pnpm with workspaces

## Development Commands

### Common Development Commands
```bash
# Install dependencies
pnpm install

# Start all applications in development mode
pnpm dev

# Build all applications
pnpm build

# Check TypeScript types across all apps
pnpm check-types

# Run linting and formatting (Biome + Ultracite)
pnpm check

# Database operations
pnpm db:push      # Push schema changes to database
pnpm db:studio    # Open database studio UI
pnpm db:generate  # Generate TypeScript types from schema
pnpm db:migrate   # Run database migrations
```

### App-Specific Commands
```bash
# Start only web application (port 3001)
pnpm dev:web

# Start only server (port 3000)
pnpm dev:server

# Native development (if applicable)
pnpm dev:native
```

## Key Development Patterns

### Code Quality Standards
- Uses Biome for linting and formatting with Ultracite rules
- Strict TypeScript configuration enabled
- Follows comprehensive accessibility (a11y) rules
- Zero configuration philosophy - everything is pre-configured

### Frontend (apps/web)
- **Framework**: TanStack Start with SSR support
- **Routing**: TanStack Router with file-based routing
- **State Management**: TanStack Query for server state
- **Styling**: Tailwind CSS with utility-first approach
- **Components**: Radix UI for accessible component library
- **Build**: Vite with TanStack Start plugin

### Backend (apps/server)
- **Framework**: Hono for lightweight, performant HTTP server
- **API**: ORPC for end-to-end type-safe APIs with OpenAPI integration
- **Database**: Drizzle ORM with PostgreSQL
- **Authentication**: Better-Auth integrated
- **Runtime**: Bun for development and production

### Database Schema
- Uses Drizzle ORM with TypeScript-first schema definitions
- Automatic migrations via `drizzle-kit`
- Schema files typically located in `apps/server/db/schema/`

### Type Safety
- Full end-to-end type safety from database to frontend
- ORPC ensures API types are shared between client and server
- Zod for runtime validation
- Strict TypeScript configuration across all apps

## File Structure Patterns

### Routing and Structure
- Frontend uses file-based routing in `apps/web/src/routes/`
- Backend uses router composition in `apps/server/routers/`
- Database schema in `apps/server/db/schema/`
- Shared types typically defined in `apps/server/src/types/`

### Configuration Files
- `biome.json` - Code quality configuration (extends Ultracite)
- `turbo.json` - Turborepo build orchestration
- `tsconfig.json` - Base TypeScript config (strict mode enabled)
- Each app has its own `tsconfig.json` and app-specific configs

## Important Notes

- All commands should be run from the root directory using `pnpm`
- The project uses strict null checks and TypeScript safety
- ORPC provides automatic type safety between frontend and backend APIs
- Database operations require proper environment configuration in `apps/server/.env`
- The project follows the Ultracite code quality rules which are enforced automatically