// Better-T-Stack configuration file
// safe to delete

{
  "$schema": "https://r2.better-t-stack.dev/schema.json",
  "version": "2.45.3",
  "createdAt": "2025-09-14T03:21:32.289Z",
  "database": "postgres",
  "orm": "drizzle",
  "backend": "hono",
  "runtime": "bun",
  "frontend": [
    "tanstack-start"
  ],
  "addons": [
    "fumadocs",
    "turborepo",
    "ultracite"
  ],
  "examples": [
    "ai",
    "todo"
  ],
  "auth": "better-auth",
  "packageManager": "pnpm",
  "dbSetup": "neon",
  "api": "orpc",
  "webDeploy": "none",
  "serverDeploy": "none"
}